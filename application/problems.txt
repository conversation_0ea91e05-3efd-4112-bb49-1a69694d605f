
> wizlop@0.1.0 build:webpack
> webpack --config config/webpack/webpack.dev.js

asset bundle.js 4.45 MiB [compared for emit] [big] (name: main)
runtime modules 1.04 KiB 5 modules
modules by path ./node_modules/ 1.48 MiB 109 modules
modules by path ./app/ 107 KiB
  modules by path ./app/landing/components/ 85.6 KiB
    modules by path ./app/landing/components/hero/*.tsx 36.2 KiB 4 modules
    modules by path ./app/landing/components/features/*.tsx 42 KiB 3 modules
    + 3 modules
  ./app/page.tsx 1.13 KiB [built] [code generated]
  ./app/colors.ts 3.55 KiB [built] [code generated]
  ./app/shared/poi/constants.ts 12.1 KiB [built] [code generated]
  ./app/landing/utils/responsiveUtils.ts 5.08 KiB [built] [code generated]
webpack 5.100.1 compiled successfully in 3483 ms
