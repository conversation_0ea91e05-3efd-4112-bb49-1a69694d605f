
> wizlop@0.1.0 build:webpack
> webpack --config config/webpack/webpack.dev.js

asset bundle.js 4.46 MiB [emitted] [big] (name: main)
runtime modules 1.04 KiB 5 modules
modules by path ./node_modules/ 1.48 MiB 109 modules
modules by path ./app/ 110 KiB
  modules by path ./app/landing/components/ 85.8 KiB
    modules by path ./app/landing/components/hero/*.tsx 35.8 KiB 4 modules
    modules by path ./app/landing/components/features/*.tsx 42.6 KiB 3 modules
    + 3 modules
  ./app/page.tsx 1.13 KiB [built] [code generated]
  ./app/colors.ts 3.55 KiB [built] [code generated]
  ./app/landing/utils/responsiveUtils.ts 7.79 KiB [built] [code generated]
  ./app/shared/poi/constants.ts 12.1 KiB [built] [code generated]
webpack 5.100.1 compiled successfully in 3545 ms
