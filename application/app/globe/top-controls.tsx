/** @format */

'use client';

import { colors } from '@/app/colors';
import { useRouter } from 'next/navigation';
import {
	FaCog,
	FaComments,
	FaHome,
	FaInfo,
	FaLocationArrow,
	FaMapMarkerAlt,
	FaSignOutAlt,
	FaSync,
	FaTimes,
	FaUser,
} from 'react-icons/fa';
import { FiZap } from 'react-icons/fi';

interface TopControlsProps {
	userLocation: {
		latitude: number;
		longitude: number;
		accuracy?: number;
		timestamp: number;
		source: 'auto' | 'manual';
		label?: string;
		liveLocationEnabled?: boolean;
	} | null;
	showSettings: boolean;
	setShowSettings: (show: boolean) => void;
	shouldFlashInfo: boolean;
	flashCount: number;
	showInfo: boolean;
	setShowInfo: (show: boolean) => void;
	setShowNavButtons: (show: boolean) => void;
	setShowBottomInstructions: (show: boolean) => void;
	setShowGlobeTitle: (show: boolean) => void;
	setShouldFlashInfo: (flash: boolean) => void;
	setFlashCount: (count: number) => void;
	setUserManuallyClickedInfo: (clicked: boolean) => void;
	handleGoToUserLocation: () => void;
	locationLoading: boolean;
	locationError: string | null;
	requestAutoLocation: () => void;
	formatLocation: () => string;
	handleLogout: () => Promise<void>;
}

export default function TopControls({
	userLocation,
	showSettings,
	setShowSettings,
	shouldFlashInfo,
	flashCount,
	showInfo,
	setShowInfo,
	setShowNavButtons,
	setShowBottomInstructions,
	setShowGlobeTitle,
	setShouldFlashInfo,
	setFlashCount,
	setUserManuallyClickedInfo,
	handleGoToUserLocation,
	locationLoading,
	locationError,
	requestAutoLocation,
	formatLocation,
	handleLogout,
}: TopControlsProps) {
	const router = useRouter();

	return (
		<div
			className='fixed left-6 z-[60] mt-20 rounded-2xl bg-white/90 backdrop-blur-md shadow-xl flex items-center gap-3 px-4 py-2 border border-gray-200'
			style={{ minWidth: 0, top: 0 }}>
			{/* Home/Landing Page */}
			<button
				onClick={() => router.push('/')}
				className='bg-white/80 text-slate-700 p-2 rounded-xl hover:bg-gray-100 transition-all duration-300 shadow-md'
				title='Home'>
				<FaHome className='w-5 h-5' />
			</button>

			{/* Chat Page */}
			<button
				onClick={() => router.push('/chat')}
				className='bg-white/80 text-slate-700 p-2 rounded-xl hover:bg-gray-100 transition-all duration-300 shadow-md'
				title='Chat'>
				<FaComments className='w-5 h-5' />
			</button>

			<button
				onClick={() => router.back()}
				className='bg-white/80 text-slate-700 p-2 rounded-xl hover:bg-gray-100 transition-all duration-300 shadow-md'
				title='Back'>
				<FaTimes className='w-5 h-5' />
			</button>

			<button
				onClick={handleGoToUserLocation}
				className={`p-2 rounded-xl transition-all duration-300 shadow-md ${
					userLocation
						? 'bg-white/80 text-slate-700 hover:bg-gray-100'
						: 'bg-gray-300/80 text-gray-500 cursor-not-allowed'
				}`}
				title={userLocation ? 'Go to Your Location' : 'No location available'}
				disabled={!userLocation}>
				<FaLocationArrow className='w-5 h-5' />
			</button>
			<button
				onClick={() => {
					setShowInfo(!showInfo);
					setShowNavButtons(true);
					setShowBottomInstructions(true);
					setShowGlobeTitle(true);
					setShouldFlashInfo(false);
					setFlashCount(0);
					setUserManuallyClickedInfo(!showInfo);
				}}
				className={`p-2 rounded-xl transition-all duration-300 shadow-md ${
					shouldFlashInfo && flashCount % 2 === 1
						? 'bg-blue-500 text-white hover:bg-blue-600'
						: 'bg-white/80 text-slate-700 hover:bg-gray-100'
				}`}
				style={
					shouldFlashInfo && flashCount % 2 === 1
						? {
								backgroundColor: colors.brand.blue,
								color: colors.neutral.cloudWhite,
								boxShadow: `0 0 20px ${colors.brand.blue}50`,
						  }
						: {}
				}
				title='Toggle Info & Navigation'>
				<FaInfo className='w-5 h-5' />
			</button>
			<div className='relative settings-dropdown'>
				<button
					onClick={() => setShowSettings(!showSettings)}
					className='bg-white/80 text-slate-700 p-2 rounded-xl hover:bg-gray-100 transition-all duration-300 shadow-md'
					title='Settings'>
					<FaCog className='w-5 h-5' />
				</button>

				{showSettings && (
					<div className='absolute right-0 mt-2 w-80 bg-white/95 backdrop-blur-sm border border-slate-200 rounded-xl shadow-xl z-50 py-2 text-sm overflow-hidden'>
						{/* Location Display */}
						<div className='px-4 py-3 bg-slate-50 border-b border-slate-100'>
							<div className='flex items-start gap-3'>
								<FaMapMarkerAlt className='text-slate-600 mt-0.5 flex-shrink-0' />
								<div className='min-w-0 flex-1'>
									<div className='flex items-center justify-between mb-1'>
										<div className='flex items-center gap-2'>
											<span className='font-medium text-slate-800'>
												Your Location
											</span>
											{userLocation && (
												<div
													className='w-2 h-2 bg-green-500 rounded-full animate-pulse'
													title='Location active'></div>
											)}
										</div>
										<button
											onClick={requestAutoLocation}
											disabled={locationLoading}
											className='text-slate-600 hover:text-slate-800 disabled:opacity-50 transition-colors'
											title='Refresh location'>
											<FaSync
												className={`w-3 h-3 ${
													locationLoading ? 'animate-spin' : ''
												}`}
											/>
										</button>
									</div>
									<div className='text-xs text-slate-600 break-all'>
										{locationLoading ? (
											<span className='text-slate-500'>
												📍 Getting location...
											</span>
										) : locationError ? (
											<span className='text-red-600'>⚠️ {locationError}</span>
										) : (
											formatLocation()
										)}
									</div>
								</div>
							</div>
						</div>

						{/* Menu Items */}
						<button
							onClick={() => {
								setShowSettings(false);
								router.push('/profile');
							}}
							className='w-full flex items-center gap-3 px-4 py-3 hover:bg-slate-50 transition-colors text-slate-700 hover:text-slate-900'>
							<FaUser className='text-base' /> Profile
						</button>
						<button
							onClick={() => {
								setShowSettings(false);
								router.push('/credits');
							}}
							className='w-full flex items-center gap-3 px-4 py-3 hover:bg-slate-50 transition-colors text-slate-700 hover:text-slate-900'>
							<FiZap className='text-base' /> Credits
						</button>
						<button
							onClick={() => {
								setShowSettings(false);
								router.push('/settings');
							}}
							className='w-full flex items-center gap-3 px-4 py-3 hover:bg-slate-50 transition-colors text-slate-700 hover:text-slate-900'>
							<FaCog className='text-base' /> Settings
						</button>
						<div className='border-t border-slate-200 my-1' />
						<button
							onClick={handleLogout}
							className='w-full flex items-center gap-3 px-4 py-3 hover:bg-red-50 transition-colors text-red-600 hover:text-red-700'>
							<FaSignOutAlt className='text-base' /> Logout
						</button>
					</div>
				)}
			</div>
			<button
				onClick={() => router.push('/chat')}
				className='bg-white/80 text-slate-700 p-2 rounded-xl hover:bg-gray-100 transition-all duration-300 shadow-md'
				title='Go to Chat'>
				<FaComments className='w-5 h-5' />
			</button>
		</div>
	);
}
