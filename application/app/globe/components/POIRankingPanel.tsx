/** @format */

'use client';

import { useRouter } from 'next/navigation';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { FaMapMarkerAlt, FaTimes } from 'react-icons/fa';
import POICard from '../../shared/cards/components/POICard';
import { useBatchMedia } from '../../shared/hooks/useBatchMedia';
import POISimpleFilter from '../../shared/poi/components/POISimpleFilter';
import { useBatchInteractions } from '../../shared/userInteractions/hooks/useBatchInteractions';

interface POI {
	id?: number; // BIGINT from spatial schema
	poi_id?: number; // BIGINT from spatial_schema.pois.id - make optional to match API responses
	temp_id?: number; // BIGINT from spatial_schema.user_pois_temp.id
	approved_id?: number; // BIGINT from spatial_schema.user_pois_approved.id
	poi_type: string;
	name: string;
	category: string;
	subcategory: string;
	city: string;
	district: string;
	latitude: number;
	longitude: number;
	neighborhood?: string;
	// 🚀 OPTIMIZATION: Interaction data from API
	like_count?: number;
	favorite_count?: number;
	visit_count?: number;
	review_count?: number;
	user_has_liked?: boolean;
	user_has_favorited?: boolean;
	user_has_visited?: boolean;
}

interface POIRankingPanelProps {
	isVisible: boolean;
	onClose: () => void;
	locationName: string;
	locationType: 'country' | 'city';
	pois: POI[];
	isLoading: boolean;
	onCitySelect?: (cityName: string) => void;
	hasMoreData?: boolean;
}

export default function POIRankingPanel({
	isVisible,
	onClose,
	locationName,
	locationType,
	pois,
	isLoading,
	hasMoreData = true,
}: POIRankingPanelProps) {
	const router = useRouter();
	const [displayedPOIs, setDisplayedPOIs] = useState<POI[]>([]);
	const [isLoadingMore, setIsLoadingMore] = useState(false);
	const [hasMore, setHasMore] = useState(hasMoreData);

	// Batch media for optimized loading (interactions come from API)
	// Note: useBatchMedia and POICard are imported at the top
	const {
		getInteractionData, // Fallback only
	} = useBatchInteractions();
	const { loadMedia, getMediaForPoi } = useBatchMedia();
	const [filters, setFilters] = useState({
		searchTerm: '',
		category: '',
		subcategory: '',
		city: '',
		district: '',
		neighborhood: '',
		country: locationName, // Set country based on location
	});
	const [isFetching, setIsFetching] = useState(false);
	const prevVisible = useRef(isVisible);

	// Refs for infinite scroll
	const observerRef = useRef<IntersectionObserver | null>(null);
	const loadingRef = useRef<HTMLDivElement | null>(null);

	// Handle filter changes
	const handleFiltersChange = useCallback(
		(newFilters: {
			city?: string;
			district?: string;
			neighborhood?: string;
		}) => {
			setFilters((prev) => ({
				...prev,
				...newFilters,
				country: locationName, // Always keep country as the selected location
			}));
		},
		[locationName]
	);

	// Get unique cities from POIs (for future use)
	useMemo(
		() => [
			'all',
			...Array.from(new Set(pois.map((poi) => poi.city).filter(Boolean))),
		],
		[pois]
	);

	// Get unique categories from POIs (for future use)
	useMemo(
		() => ['all', ...Array.from(new Set(pois.map((poi) => poi.category)))],
		[pois]
	);

	// Helper function to get unique POI ID - handle multiple ID types
	const getPOIId = useCallback((poi: POI, index?: number) => {
		// Use poi_id if available, otherwise fall back to other IDs or index
		const id = poi.poi_id || poi.id || poi.temp_id || poi.approved_id || index;
		return `${poi.poi_type || 'unknown'}_${id}`;
	}, []);

	// Initialize displayed POIs when pois change (initial load)
	useEffect(() => {
		if (pois.length > 0) {
			setDisplayedPOIs(pois);
			setHasMore(hasMoreData);

			// POIs loaded successfully
		}
	}, [pois, hasMoreData]);

	// Reset location filters when panel becomes visible
	useEffect(() => {
		if (isVisible && !prevVisible.current) {
			// Don't reset filters immediately - let the component show initial data first
		}
		prevVisible.current = isVisible;
	}, [isVisible, pois.length]);

	// Fetch POIs when filters change
	useEffect(() => {
		// Check if any filters are applied (excluding country which is always set)
		const hasFilters =
			filters.searchTerm ||
			filters.category ||
			filters.subcategory ||
			filters.city ||
			filters.district ||
			filters.neighborhood;

		if (hasFilters) {
			const fetchPOIs = async () => {
				setIsFetching(true);

				// Use the same filter API as POI page - it supports all filters
				const params = new URLSearchParams();
				if (filters.searchTerm) params.append('name', filters.searchTerm);
				if (filters.category) params.append('category', filters.category);
				if (filters.subcategory)
					params.append('subcategory', filters.subcategory);
				if (filters.city) params.append('city', filters.city);
				if (filters.district) params.append('district', filters.district);
				if (filters.neighborhood)
					params.append('neighborhood', filters.neighborhood);
				params.append('limit', '20');
				params.append('page', '1');
				params.append('includeInteractions', 'true'); // ✅ Include interaction data like POI page

				const response = await fetch(`/api/pois/filter?${params.toString()}`, {
					credentials: 'include', // ✅ Include cookies for authentication
				});
				const data = await response.json();

				if (data.success) {
					setDisplayedPOIs(data.pois || []);
					setHasMore(data.pois && data.pois.length === 20);
				}
				setIsFetching(false);
			};
			fetchPOIs();
		} else {
			// No filters applied, use the original pois data from props
			setDisplayedPOIs(pois);
			setHasMore(hasMoreData || false);
		}
	}, [filters, pois, hasMoreData]);

	// Load more POIs for infinite scroll
	const loadMorePOIs = useCallback(async () => {
		if (isLoadingMore || !hasMore) return;

		setIsLoadingMore(true);

		try {
			let apiUrl = '';
			const params = new URLSearchParams();

			// Check if any filters are applied
			const hasFilters =
				filters.searchTerm ||
				filters.category ||
				filters.subcategory ||
				filters.city ||
				filters.district ||
				filters.neighborhood;

			if (hasFilters) {
				// Use globe API for filtered data
				apiUrl = '/api/pois/globe';
				if (filters.searchTerm) params.append('name', filters.searchTerm);
				if (filters.category) params.append('category', filters.category);
				if (filters.subcategory)
					params.append('subcategory', filters.subcategory);
				if (filters.city) params.append('city', filters.city);
				if (filters.district) params.append('district', filters.district);
				if (filters.neighborhood)
					params.append('neighborhood', filters.neighborhood);
				if (filters.country) params.append('country', filters.country);
			} else {
				// Use rankings API for country-level data
				apiUrl = '/api/pois/rankings';
			}

			// Add pagination params
			params.append('limit', '20');
			params.append('offset', displayedPOIs.length.toString());

			// Category filter is already added above in the hasFilters section

			let response;
			if (apiUrl === '/api/pois/rankings') {
				// POST request for rankings API
				response = await fetch(apiUrl, {
					method: 'POST',
					headers: { 'Content-Type': 'application/json' },
					credentials: 'include', // ✅ Include cookies for authentication
					body: JSON.stringify({
						locationType: 'country',
						locationName: locationName,
						center: { latitude: 39.9334, longitude: 32.8597 }, // Turkey center
						zoom: 100,
						offset: displayedPOIs.length,
						limit: 20,
						category: filters.category || undefined,
						includeInteractions: true, // ✅ Include interaction data like POI page
					}),
				});
			} else {
				// GET request for globe API
				response = await fetch(`${apiUrl}?${params.toString()}`, {
					credentials: 'include', // ✅ Include cookies for authentication
				});
			}

			const data = await response.json();

			if (data.success && data.pois && data.pois.length > 0) {
				// Filter out duplicates using the robust ID function
				const existingIds = new Set(displayedPOIs.map((poi) => getPOIId(poi)));
				const newPOIs = data.pois.filter(
					(poi: Record<string, unknown>) =>
						!existingIds.has(getPOIId(poi as unknown as POI))
				);

				if (newPOIs.length > 0) {
					setDisplayedPOIs((prev) => [...prev, ...newPOIs]);
					setHasMore(newPOIs.length === 20); // Has more if we got a full batch
				} else {
					setHasMore(false);
				}
			} else {
				setHasMore(false);
			}
		} catch (error) {
			console.error('Failed to load more POIs:', error);
			setHasMore(false);
		} finally {
			setIsLoadingMore(false);
		}
	}, [isLoadingMore, hasMore, filters, displayedPOIs.length, locationName]);

	// Intersection Observer for infinite scroll
	useEffect(() => {
		if (observerRef.current) {
			observerRef.current.disconnect();
		}

		observerRef.current = new IntersectionObserver(
			(entries) => {
				if (
					entries[0].isIntersecting &&
					hasMore &&
					!isLoadingMore &&
					!isFetching
				) {
					loadMorePOIs();
				}
			},
			{ threshold: 0.1 }
		);

		if (loadingRef.current) {
			observerRef.current.observe(loadingRef.current);
		}

		return () => {
			if (observerRef.current) {
				observerRef.current.disconnect();
			}
		};
	}, [hasMore, isLoadingMore, isFetching, loadMorePOIs]);

	// Legacy scroll handler removed - using Intersection Observer instead

	// Reset when pois change (this is handled by the filters useEffect above)

	// POIs are already filtered by the API, so we just use displayedPOIs directly

	const handlePOIClick = useCallback(
		(poi: POI) => {
			// Route to POI profile page - handle different POI types and ID fields
			const poiId = poi.poi_id || poi.id || poi.temp_id || poi.approved_id;
			if (!poiId) {
				console.warn('POI has no valid ID:', poi);
				return;
			}

			// Determine POI type for routing
			const poiType =
				poi.poi_type === 'user_temp'
					? 'user-temp'
					: poi.poi_type === 'user_approved'
					? 'user-approved'
					: 'official';

			router.push(`/pois/${poiType}/${poiId}`);
		},
		[router]
	);

	// getCategoryIcon function removed as it was unused

	// Load media when POIs are displayed (interactions come from API)
	useEffect(() => {
		if (displayedPOIs.length > 0) {
			const poisForMedia = displayedPOIs.map((poi) => ({
				poi_id: (
					poi.poi_id ||
					poi.id ||
					poi.temp_id ||
					poi.approved_id ||
					0
				).toString(),
				poi_type: poi.poi_type,
			}));
			loadMedia(poisForMedia);
		}
	}, [displayedPOIs, loadMedia]);

	// Removed handleToggleInteraction - POICard now handles interactions internally

	const getCategoryColor = (category: string, subcategory?: string) => {
		// First try subcategory colors for more specific styling
		const subcategoryColors: { [key: string]: string } = {
			restaurant: '#E74C3C',
			cafe: '#E74C3C',
			bar: '#E74C3C',
			shop: '#3498DB',
			supermarket: '#3498DB',
			bank: '#2ECC71',
			hospital: '#2ECC71',
			school: '#2ECC71',
			hotel: '#2ECC71',
			parking: '#27AE60',
			park: '#27AE60',
			place_of_worship: '#F39C12',
			pharmacy: '#2ECC71',
			post_office: '#2ECC71',
			fuel: '#E67E22',
			bus_station: '#E67E22',
			cinema: '#E67E22',
			library: '#2ECC71',
			sport: '#E67E22',
		};

		// Category-level colors as fallback
		const categoryColors: { [key: string]: string } = {
			'Food & Drink': '#E74C3C',
			Shopping: '#3498DB',
			Entertainment: '#E67E22',
			'Health & Medical': '#2ECC71',
			Education: '#2ECC71',
			Transportation: '#E67E22',
			Services: '#2ECC71',
			Tourism: '#F39C12',
			'Sports & Recreation': '#E67E22',
			Other: '#95A5A6',
		};

		// Try subcategory first, then category, then default
		if (subcategory && subcategoryColors[subcategory]) {
			return subcategoryColors[subcategory];
		}
		return categoryColors[category] || '#95A5A6';
	};

	// Helper function to convert POI to format expected by POICard
	const convertToBasePOI = useCallback((poi: POI) => {
		return {
			id: poi.poi_id || poi.id || poi.temp_id || poi.approved_id || 0,
			poi_type: poi.poi_type,
			poi_id: poi.poi_id || poi.id || undefined,
			temp_id: poi.temp_id || undefined,
			approved_id: poi.approved_id || undefined,
			name: poi.name,
			category: poi.category || '',
			subcategory: poi.subcategory,
			city: poi.city,
			district: poi.district,
			neighborhood: poi.neighborhood,
			country: (poi as POI & { country?: string }).country,
			latitude: poi.latitude || 0,
			longitude: poi.longitude || 0,
			phone_number: (poi as POI & { phone_number?: string }).phone_number,
			opening_hours: (poi as POI & { opening_hours?: string }).opening_hours,
			is_favorite: false, // Will be loaded via batch interactions
		};
	}, []);

	// Reset filters when panel is opened
	useEffect(() => {
		if (!prevVisible.current && isVisible) {
			setFilters(() => ({
				searchTerm: '',
				category: '',
				subcategory: '',
				city: '',
				district: '',
				neighborhood: '',
				country: locationName, // Keep country as the selected location
			}));
		}
		prevVisible.current = isVisible;
	}, [isVisible, locationName]);

	return (
		<div
			className={`fixed top-0 right-0 h-full w-96 bg-white shadow-2xl transform transition-transform duration-300 ease-in-out z-50 overflow-hidden ${
				isVisible ? 'translate-x-0' : 'translate-x-full'
			}`}
			onClick={(e) => e.stopPropagation()}>
			{/* Header */}
			<div className='bg-gradient-to-r from-blue-500 to-green-500 text-white p-6'>
				<div className='flex items-center justify-between mb-4'>
					<div>
						<h2 className='text-xl font-bold'>{locationName}</h2>
						<div className='flex items-center gap-2'>
							<p className='text-blue-100 text-sm capitalize'>
								{locationType} Rankings
							</p>
							{isLoading && (
								<div className='flex items-center'>
									<div className='animate-spin rounded-full h-3 w-3 border-b border-white mr-1'></div>
									<span className='text-blue-100 text-sm'>Loading...</span>
								</div>
							)}
						</div>
					</div>
					<button
						onClick={onClose}
						className='text-white hover:text-blue-100 transition-colors'>
						<FaTimes className='w-5 h-5' />
					</button>
				</div>

				{/* Filters are now handled by POISimpleFilter below */}
			</div>

			{/* Content */}
			<div
				className='h-full overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100'
				style={{
					height: 'calc(100vh - 200px)', // Adjust for header height
					overscrollBehavior: 'contain', // Prevent scroll chaining
					willChange: 'scroll-position', // Optimize for scrolling
					transform: 'translateZ(0)', // Force hardware acceleration
				}}>
				{/* Filter Component */}
				<div className='p-4 border-b border-gray-200'>
					<POISimpleFilter
						onFiltersChange={handleFiltersChange}
						showSearch={true}
						showCategory={true}
						showLocation={true}
						layout='vertical'
						placeholder='Search POIs by name...'
					/>
				</div>

				{isLoading ? (
					<div className='flex items-center justify-center h-64'>
						<div className='text-center'>
							<div className='animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4'></div>
							<p className='text-gray-600'>Loading rankings...</p>
						</div>
					</div>
				) : displayedPOIs.length === 0 ? (
					<div className='flex items-center justify-center h-64'>
						<div className='text-center text-gray-500'>
							<FaMapMarkerAlt className='w-12 h-12 mx-auto mb-4 text-gray-300' />
							<p>No places found in this area</p>
						</div>
					</div>
				) : (
					<div className='p-4 space-y-4'>
						{displayedPOIs.map((poi, index) => (
							<div
								key={`${getPOIId(poi, index)}-${index}`}
								className='relative'
								style={{
									animationName: 'fadeInUp',
									animationDuration: '0.6s',
									animationTimingFunction: 'ease-out',
									animationFillMode: 'forwards',
									animationDelay: `${index * 50}ms`,
								}}>
								{/* Ranking Badge */}
								<div className='absolute top-2 left-2 z-10'>
									<div
										className='flex items-center justify-center w-8 h-8 rounded-full text-white text-sm font-bold shadow-lg'
										style={{
											backgroundColor: getCategoryColor(
												poi.category,
												poi.subcategory
											),
										}}>
										{index + 1}
									</div>
								</div>

								{/* Unified POI Card */}
								<POICard
									poi={convertToBasePOI(poi)}
									isVisible={true}
									onClose={() => {}}
									onNavigate={() => handlePOIClick(poi)}
									variant='inline'
									showActions={true}
									interactionData={(() => {
										const poiId =
											poi.poi_id ||
											poi.id ||
											poi.temp_id ||
											poi.approved_id ||
											0;

										// 🚀 OPTIMIZATION: Use interaction data from API response (like POI page)
										if (poi.like_count !== undefined) {
											const interactionData = {
												poi_id: poiId,
												poi_type: poi.poi_type,
												like_count: poi.like_count || 0,
												favorite_count: poi.favorite_count || 0,
												visit_count: poi.visit_count || 0,
												review_count: poi.review_count || 0,
												media_count: 0, // Add missing media_count field
												user_has_liked: poi.user_has_liked || false,
												user_has_favorited: poi.user_has_favorited || false,
												user_has_visited: poi.user_has_visited || false,
											};

											return interactionData;
										}

										// Fallback to batch hook (for backward compatibility)
										const fallbackData = getInteractionData({
											poi_id: poiId,
											poi_type: poi.poi_type,
										});

										// Add missing media_count field to match POIInteractionData interface
										return fallbackData
											? {
													...fallbackData,
													media_count: 0, // Default value for media_count
											  }
											: null;
									})()}
									useBatchLoading={false} // ✅ Use API data like POI page
									useSimpleUI={true}
									showPoiId={false}
									disableInteractionAutoLoad={true} // ✅ Disable auto-loading, use API data
									mediaData={getMediaForPoi(
										(
											poi.poi_id ||
											poi.id ||
											poi.temp_id ||
											poi.approved_id ||
											0
										).toString()
									)}
								/>
							</div>
						))}

						{/* Infinite Scroll Loading Indicator */}
						{hasMore && (
							<div
								ref={loadingRef}
								className='flex justify-center items-center py-6'>
								{isLoadingMore ? (
									<div className='flex items-center gap-2'>
										<div className='animate-spin rounded-full h-6 w-6 border-b-2 border-white'></div>
										<span className='text-white text-sm'>
											Loading more POIs...
										</span>
									</div>
								) : (
									<div className='text-white/60 text-sm'>
										Scroll down to load more
									</div>
								)}
							</div>
						)}

						{/* End of results indicator */}
						{!hasMore && displayedPOIs.length > 0 && (
							<div className='text-center py-6'>
								<div className='text-white/60 text-sm'>
									You've reached the end! Found {displayedPOIs.length} POI
									{displayedPOIs.length !== 1 ? 's' : ''} total.
								</div>
							</div>
						)}

						{/* Show loading spinner if fetching initial data */}
						{isFetching && displayedPOIs.length === 0 && (
							<div className='text-center py-4 text-white/60'>Loading...</div>
						)}
					</div>
				)}
			</div>
		</div>
	);
}
