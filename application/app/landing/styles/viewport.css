/** @format */

/* Viewport height utilities for better cross-browser support */

/* Fix for mobile browsers where 100vh doesn't account for address bar */
.viewport-height-full {
  height: 100vh;
  min-height: 100vh;
  /* Fallback for older browsers */
  min-height: -webkit-fill-available;
}

/* Support for dynamic viewport units in newer browsers */
@supports (height: 100dvh) {
  .viewport-height-full {
    height: 100dvh;
    min-height: 100dvh;
  }
}

/* Ensure content doesn't overflow on small screens */
.viewport-content {
  max-height: 100vh;
  overflow-y: auto;
}

/* Responsive viewport sections */
.viewport-section {
  min-height: 50vh;
}

@media (min-width: 768px) {
  .viewport-section {
    min-height: 60vh;
  }
}

@media (min-width: 1024px) {
  .viewport-section {
    min-height: 70vh;
  }
}

/* Fix for iOS Safari viewport issues */
@supports (-webkit-touch-callout: none) {
  .viewport-height-full {
    min-height: -webkit-fill-available;
  }
}

/* Ensure proper scaling on orientation change */
@media screen and (orientation: landscape) {
  .viewport-height-full {
    height: 100vh;
    min-height: 100vh;
  }
}

/* Prevent content from being cut off on very small screens */
@media (max-height: 500px) {
  .viewport-height-full {
    min-height: 500px;
  }
}
