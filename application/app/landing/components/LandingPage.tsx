/** @format */

import { colors } from '@/app/colors';
import React from 'react';
import AIShowcase from './features/AIShowcase';
import CategoryExplorer from './features/CategoryExplorer';
import LandingFooter from './footer/LandingFooter';
import HeroSection from './hero/HeroSection';

interface LandingPageProps {
	onGetStarted: () => void;
}

const LandingPage: React.FC<LandingPageProps> = ({ onGetStarted }) => {
	return (
		<div
			className='min-h-screen relative'
			style={{
				background: `
          linear-gradient(180deg,
            rgba(1, 3, 79, 0.2) 0%,
            rgba(163, 247, 181, 0.2) 25%,
            rgba(128, 237, 153, 0.2) 50%,
            rgba(102, 208, 255, 0.2) 75%,
            rgba(51, 194, 255, 0.2) 100%
          ),
          ${colors.neutral.cloudWhite}
        `,
			}}>
			{/* Hero Section - Two Columns */}
			<HeroSection onGetStarted={onGetStarted} />

			{/* Features Section - Category Explorer */}
			<CategoryExplorer
				onCategorySelect={(category) =>
					console.log('Selected category:', category)
				}
			/>

			{/* Features Section - AI Showcase */}
			<AIShowcase onGetStarted={onGetStarted} />

			{/* Footer Section */}
			<LandingFooter />
		</div>
	);
};

export default LandingPage;
