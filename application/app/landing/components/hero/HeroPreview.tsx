/** @format */

'use client';

import { colors } from '@/app/colors';
import {
	COMPONENT_SIZES,
	TYPOGRAPHY_SCALE,
} from '@/app/landing/utils/responsiveUtils';
import { LANDING_PAGE_DATA } from '@/app/shared/poi/constants';
import React, { useEffect, useState } from 'react';
import {
	FiCamera,
	FiCoffee,
	FiGlobe,
	FiHeart,
	FiMapPin,
	FiMessageCircle,
	FiStar,
	FiTrendingUp,
	FiZap,
} from 'react-icons/fi';

interface FloatingIslandProps {
	id: string;
	position: { x: number; y: number; z: number };
	children: React.ReactNode;
	hoverEffect: 'lift' | 'glow' | 'rotate';
	connectionLines?: string[];
	delay?: number;
}

interface HeroPreviewProps {
	onGetStarted: () => void;
}

const FloatingIsland: React.FC<FloatingIslandProps> = ({
	children,
	hoverEffect,
	delay = 0,
}) => {
	const [isHovered, setIsHovered] = useState(false);

	const getHoverStyles = () => {
		if (!isHovered) return {};

		switch (hoverEffect) {
			case 'lift':
				return {
					transform: `translateY(-8px) scale(1.02)`,
					boxShadow: `0 12px 24px rgba(51, 194, 255, 0.15)`,
				};
			case 'glow':
				return {
					transform: `scale(1.02)`,
					boxShadow: `0 8px 32px rgba(128, 237, 153, 0.2)`,
				};
			case 'rotate':
				return {
					transform: `scale(1.05) rotate(2deg)`,
					boxShadow: `0 20px 40px rgba(51, 194, 255, 0.2)`,
				};
			default:
				return {};
		}
	};

	return (
		<div
			className='w-full transition-all duration-500 ease-out'
			style={{
				animationDelay: `${delay}ms`,
				...getHoverStyles(),
			}}
			onMouseEnter={() => setIsHovered(true)}
			onMouseLeave={() => setIsHovered(false)}>
			{children}
		</div>
	);
};

const SearchPreviewCard: React.FC<{
	screenSize: 'mobile' | 'tablet' | 'desktop';
}> = ({ screenSize }) => {
	// Get responsive sizing
	const getResponsiveCardStyles = () => {
		const fontSize = TYPOGRAPHY_SCALE.cardText[screenSize];
		const titleSize = TYPOGRAPHY_SCALE.cardTitle[screenSize];
		const padding =
			screenSize === 'mobile'
				? '0.75rem'
				: screenSize === 'tablet'
				? '1rem'
				: '1.25rem';

		return {
			fontSize,
			titleSize,
			padding,
			iconSize:
				screenSize === 'mobile'
					? '1.5rem'
					: screenSize === 'tablet'
					? '2rem'
					: '2.5rem',
			spacing: screenSize === 'mobile' ? '0.75rem' : '1rem',
		};
	};

	const styles = getResponsiveCardStyles();

	return (
		<div
			className='w-full rounded-2xl border backdrop-blur-sm cursor-default shadow-lg'
			style={{
				background: `linear-gradient(135deg, ${colors.ui.blue50} 0%, ${colors.neutral.cloudWhite} 100%)`,
				borderColor: colors.ui.gray200,
				boxShadow: '0 8px 25px rgba(0, 0, 0, 0.1)',
				padding: styles.padding,
			}}>
			<div
				className='flex items-center space-x-3'
				style={{ marginBottom: styles.spacing }}>
				<div
					className='rounded-xl flex items-center justify-center flex-shrink-0'
					style={{
						background: `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.green} 100%)`,
						width: styles.iconSize,
						height: styles.iconSize,
					}}>
					<FiMapPin
						className='text-white'
						style={{
							width: screenSize === 'mobile' ? '0.75rem' : '1rem',
							height: screenSize === 'mobile' ? '0.75rem' : '1rem',
						}}
					/>
				</div>
				<div className='min-w-0 flex-1'>
					<h3
						className='font-semibold truncate'
						style={{
							color: colors.neutral.textBlack,
							fontSize: styles.titleSize,
						}}>
						{LANDING_PAGE_DATA.searchPreview.title}
					</h3>
					<p
						className='truncate'
						style={{
							color: colors.neutral.slateGray,
							fontSize: styles.fontSize,
						}}>
						{LANDING_PAGE_DATA.searchPreview.subtitle}
					</p>
				</div>
			</div>

			<div className='space-y-2 sm:space-y-3'>
				{LANDING_PAGE_DATA.searchPreview.locations.map((location, index) => (
					<div
						key={index}
						className='flex items-center justify-between p-2 sm:p-3 rounded-lg transition-all duration-300 hover:scale-102'
						style={{ background: colors.ui.blue50 }}>
						<div className='flex items-center space-x-2 sm:space-x-3 min-w-0 flex-1'>
							<div
								className='w-2 h-2 rounded-full flex-shrink-0 animate-pulse'
								style={{
									background: colors.brand.green,
									animationDelay: `${index * 0.2}s`,
								}}></div>
							<div className='min-w-0 flex-1'>
								<p
									className='font-medium text-xs sm:text-sm truncate'
									style={{ color: colors.neutral.textBlack }}>
									{location.name}
								</p>
								<p
									className='text-xs truncate'
									style={{ color: colors.neutral.slateGray }}>
									{location.type}
								</p>
							</div>
						</div>
						<div className='flex items-center space-x-1 flex-shrink-0'>
							<FiStar
								className='w-3 h-3'
								style={{ color: colors.brand.green }}
							/>
							<span
								className='text-xs font-medium'
								style={{ color: colors.neutral.textBlack }}>
								{location.rating}
							</span>
						</div>
					</div>
				))}
			</div>
		</div>
	);
};

const AIChatBubble: React.FC = () => {
	const [currentExampleIndex, setCurrentExampleIndex] = useState(0);
	const [isTyping, setIsTyping] = useState(false);
	const [showUserMessage, setShowUserMessage] = useState(true);
	const [showAIResponse, setShowAIResponse] = useState(false);
	const [userText, setUserText] = useState('');
	const [aiText, setAIText] = useState('');

	const examples = LANDING_PAGE_DATA.conversationExamples;
	const currentExample = examples[currentExampleIndex];

	// Animation cycle effect
	React.useEffect(() => {
		const animationCycle = async () => {
			// Reset states
			setShowUserMessage(false);
			setShowAIResponse(false);
			setUserText('');
			setAIText('');
			setIsTyping(false);

			// Wait a bit before starting
			await new Promise((resolve) => setTimeout(resolve, 500));

			// Type user message
			setShowUserMessage(true);
			for (let i = 0; i <= currentExample.userMessage.length; i++) {
				setUserText(currentExample.userMessage.slice(0, i));
				await new Promise((resolve) => setTimeout(resolve, 50));
			}

			// Wait before AI response
			await new Promise((resolve) => setTimeout(resolve, 800));

			// Show typing indicator
			setIsTyping(true);
			await new Promise((resolve) => setTimeout(resolve, 1000));

			// Type AI response
			setIsTyping(false);
			setShowAIResponse(true);
			for (let i = 0; i <= currentExample.aiResponse.length; i++) {
				setAIText(currentExample.aiResponse.slice(0, i));
				await new Promise((resolve) => setTimeout(resolve, 30));
			}

			// Wait before next cycle
			await new Promise((resolve) => setTimeout(resolve, 4000));

			// Move to next example
			setCurrentExampleIndex((prev) => (prev + 1) % examples.length);
		};

		animationCycle();
	}, [
		currentExampleIndex,
		currentExample.userMessage,
		currentExample.aiResponse,
		examples.length,
	]);

	return (
		<div
			className='w-full max-w-sm p-4 sm:p-5 rounded-2xl border backdrop-blur-sm cursor-default shadow-lg transition-all duration-500'
			style={{
				background: `linear-gradient(135deg, ${colors.ui.green50} 0%, ${colors.neutral.cloudWhite} 100%)`,
				borderColor: colors.ui.gray200,
				boxShadow: '0 8px 25px rgba(0, 0, 0, 0.1)',
				width: '384px', // Fixed width to prevent expansion
				minWidth: '384px',
				maxWidth: '384px',
			}}>
			<div className='flex items-center space-x-3 mb-4'>
				<div
					className='w-8 h-8 sm:w-10 sm:h-10 rounded-xl flex items-center justify-center flex-shrink-0'
					style={{
						background: `linear-gradient(135deg, ${colors.brand.green} 0%, ${colors.brand.blue} 100%)`,
					}}>
					<FiMessageCircle className='w-4 h-4 sm:w-5 sm:h-5 text-white' />
				</div>
				<div className='min-w-0 flex-1'>
					<h3
						className='font-semibold text-sm sm:text-base truncate'
						style={{ color: colors.neutral.textBlack }}>
						{LANDING_PAGE_DATA.aiConversation.title}
					</h3>
					<p
						className='text-xs sm:text-sm truncate'
						style={{ color: colors.neutral.slateGray }}>
						{LANDING_PAGE_DATA.aiConversation.subtitle}
					</p>
				</div>
			</div>

			<div className='space-y-3 sm:space-y-4'>
				{/* User message */}
				{showUserMessage && (
					<div className='flex justify-end'>
						<div
							className='p-2 sm:p-3 rounded-2xl rounded-tr-sm'
							style={{
								background: colors.brand.blue,
								color: 'white',
								width: '85%',
								maxWidth: '85%',
								wordWrap: 'break-word',
								overflowWrap: 'break-word',
							}}>
							<p
								className='text-xs sm:text-sm'
								style={{ margin: 0 }}>
								{userText}
								<span className='animate-pulse'>|</span>
							</p>
						</div>
					</div>
				)}

				{/* AI response or typing indicator */}
				{(showAIResponse || isTyping) && (
					<div className='flex justify-start'>
						<div
							className='p-2 sm:p-3 rounded-2xl rounded-tl-sm'
							style={{
								background: colors.ui.gray100,
								color: colors.neutral.textBlack,
								width: '85%',
								maxWidth: '85%',
								wordWrap: 'break-word',
								overflowWrap: 'break-word',
							}}>
							{isTyping ? (
								<div
									className='flex items-center space-x-2'
									style={{ height: '20px' }}>
									<div className='flex space-x-1'>
										<div
											className='w-1 h-1 rounded-full animate-pulse'
											style={{ background: colors.brand.green }}></div>
										<div
											className='w-1 h-1 rounded-full animate-pulse'
											style={{
												background: colors.brand.green,
												animationDelay: '0.2s',
											}}></div>
										<div
											className='w-1 h-1 rounded-full animate-pulse'
											style={{
												background: colors.brand.green,
												animationDelay: '0.4s',
											}}></div>
									</div>
									<span
										className='text-xs'
										style={{ color: colors.neutral.slateGray }}>
										{LANDING_PAGE_DATA.aiConversation.typingIndicator}
									</span>
								</div>
							) : (
								<div style={{ minHeight: '20px' }}>
									<p
										className='text-xs sm:text-sm'
										style={{ margin: 0 }}>
										{aiText}
										<span className='animate-pulse'>|</span>
									</p>
								</div>
							)}
						</div>
					</div>
				)}
			</div>
		</div>
	);
};

const CentralCTAHub: React.FC<{ onGetStarted: () => void }> = ({
	onGetStarted,
}) => {
	const [isHovered, setIsHovered] = useState(false);

	const iconData = [
		{ icon: FiCoffee, angle: 0, radius: 140, animationDelay: 0 },
		{ icon: FiCamera, angle: 72, radius: 140, animationDelay: 0.1 },
		{ icon: FiHeart, angle: 144, radius: 140, animationDelay: 0.2 },
		{ icon: FiZap, angle: 216, radius: 140, animationDelay: 0.3 },
		{ icon: FiGlobe, angle: 288, radius: 140, animationDelay: 0.4 },
	];

	return (
		<div className='flex flex-col items-center space-y-6'>
			{/* Main Hub Circle - Responsive */}
			<div
				className='relative cursor-pointer group'
				onMouseEnter={() => setIsHovered(true)}
				onMouseLeave={() => setIsHovered(false)}
				onClick={onGetStarted}>
				{/* Main Hub Circle */}
				<div
					className='relative w-56 h-56 sm:w-64 sm:h-64 lg:w-72 lg:h-72 rounded-full flex flex-col items-center justify-center border-2 backdrop-blur-sm transition-all duration-500 mx-auto'
					style={{
						background: `linear-gradient(135deg, ${colors.neutral.cloudWhite} 0%, ${colors.ui.blue50} 100%)`,
						borderColor: colors.brand.blue,
						transform: isHovered ? 'scale(1.05)' : 'scale(1)',
						boxShadow: isHovered
							? `0 20px 40px rgba(51, 194, 255, 0.2)`
							: '0 8px 20px rgba(0, 0, 0, 0.1)',
						zIndex: 10,
					}}>
					{/* Center Icon */}
					<div
						className='w-14 h-14 sm:w-16 sm:h-16 lg:w-18 lg:h-18 rounded-full flex items-center justify-center mb-4 lg:mb-5 transition-all duration-500'
						style={{
							background: `linear-gradient(135deg, ${colors.brand.navy} 0%, ${colors.brand.blue} 100%)`,
							transform: isHovered ? 'scale(1.1) rotate(10deg)' : 'scale(1)',
						}}>
						<FiTrendingUp className='w-7 h-7 sm:w-8 sm:h-8 lg:w-9 lg:h-9 text-white' />
					</div>

					<h2
						className='text-xl sm:text-2xl lg:text-2xl font-bold text-center mb-3 px-4 transition-all duration-300'
						style={{
							color: colors.brand.navy,
							transform: isHovered ? 'scale(1.05)' : 'scale(1)',
							zIndex: 20,
							position: 'relative',
						}}>
						Start Exploring
					</h2>
					<p
						className='text-sm sm:text-base text-center px-4 lg:px-6 transition-all duration-300'
						style={{
							color: colors.neutral.slateGray,
							zIndex: 20,
							position: 'relative',
						}}>
						Discover amazing places through smart search
					</p>
				</div>

				{/* Floating icons around the hub - Responsive positioning */}
				<div className='absolute inset-0 pointer-events-none'>
					{iconData.map(
						({ icon: Icon, angle, radius, animationDelay }, index) => {
							// Responsive radius based on screen size - increased for larger circle
							const responsiveRadius = radius * 0.85; // Increased radius for better spacing
							const x = Math.cos((angle * Math.PI) / 180) * responsiveRadius;
							const y = Math.sin((angle * Math.PI) / 180) * responsiveRadius;

							return (
								<div
									key={index}
									className='absolute transition-all duration-500 hidden sm:block'
									style={{
										left: `calc(50% + ${x}px - 24px)`,
										top: `calc(50% + ${y}px - 24px)`,
										transform: isHovered ? 'scale(1.2)' : 'scale(1)',
										transitionDelay: isHovered ? `${animationDelay}s` : '0s',
										zIndex: 5,
									}}>
									{/* Icon Circle */}
									<div
										className='w-10 h-10 lg:w-12 lg:h-12 rounded-full flex items-center justify-center transition-all duration-500'
										style={{
											background: isHovered
												? `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.green} 100%)`
												: `linear-gradient(135deg, ${colors.supporting.lightBlue} 0%, ${colors.supporting.mintGreen} 100%)`,
											boxShadow: isHovered
												? `0 8px 25px rgba(51, 194, 255, 0.4)`
												: '0 4px 12px rgba(0, 0, 0, 0.1)',
											transitionDelay: isHovered ? `${animationDelay}s` : '0s',
										}}>
										<Icon
											className='w-5 h-5 lg:w-6 lg:h-6 transition-all duration-500'
											style={{
												color: isHovered ? 'white' : colors.brand.navy,
												transform: isHovered
													? `rotate(${15 + index * 5}deg) scale(1.1)`
													: 'rotate(0deg)',
												transitionDelay: isHovered
													? `${animationDelay}s`
													: '0s',
											}}
										/>
									</div>
								</div>
							);
						}
					)}
				</div>
			</div>
		</div>
	);
};

const HeroPreview: React.FC<HeroPreviewProps> = ({ onGetStarted }) => {
	const [screenSize, setScreenSize] = useState<'mobile' | 'tablet' | 'desktop'>(
		'desktop'
	);

	// Detect screen size for responsive behavior
	useEffect(() => {
		const updateScreenSize = () => {
			const width = window.innerWidth;
			if (width < 768) {
				setScreenSize('mobile');
			} else if (width < 1024) {
				setScreenSize('tablet');
			} else {
				setScreenSize('desktop');
			}
		};

		updateScreenSize();
		window.addEventListener('resize', updateScreenSize);
		return () => window.removeEventListener('resize', updateScreenSize);
	}, []);

	// Get responsive card configuration
	const getCardConfig = () => {
		return {
			width: COMPONENT_SIZES.heroCardWidth[screenSize],
			maxWidth: COMPONENT_SIZES.heroCardMaxWidth[screenSize],
			gap:
				screenSize === 'mobile'
					? '1rem'
					: screenSize === 'tablet'
					? '1.5rem'
					: '2rem',
		};
	};

	const cardConfig = getCardConfig();

	return (
		<div className='relative w-full'>
			{/* Responsive Stack Container for Preview Cards */}
			<div
				className='flex flex-col items-center justify-center'
				style={{ gap: cardConfig.gap, marginBottom: cardConfig.gap }}>
				{/* Search Preview Card - Top */}
				<div className='flex justify-center'>
					<div
						style={{ width: cardConfig.width, maxWidth: cardConfig.maxWidth }}>
						<SearchPreviewCard screenSize={screenSize} />
					</div>
				</div>

				{/* AI Chat Card - Bottom */}
				<div className='flex justify-center'>
					<div
						style={{ width: cardConfig.width, maxWidth: cardConfig.maxWidth }}>
						<AIChatBubble />
					</div>
				</div>
			</div>

			{/* Central CTA Hub */}
			<div className='flex justify-center'>
				<div style={{ width: cardConfig.width, maxWidth: cardConfig.maxWidth }}>
					<CentralCTAHub onGetStarted={onGetStarted} />
				</div>
			</div>
		</div>
	);
};

export default HeroPreview;
