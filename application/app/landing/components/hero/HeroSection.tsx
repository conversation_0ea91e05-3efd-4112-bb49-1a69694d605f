/** @format */

'use client';

import { colors } from '@/app/colors';
import { useViewportHeight } from '@/app/landing/utils/responsiveUtils';
import React from 'react';
import HeroBackground from './HeroBackground';
import HeroBranding from './HeroBranding';
import HeroPreview from './HeroPreview';

interface HeroSectionProps {
	onGetStarted: () => void;
}

const HeroSection: React.FC<HeroSectionProps> = ({ onGetStarted }) => {
	// Use the new viewport height hook for hero section
	const { screenSize, height, minHeight, maxHeight, padding } =
		useViewportHeight('hero');

	return (
		<div
			className='relative overflow-hidden bg-transparent viewport-height-full'
			style={
				{
					height,
					minHeight,
					maxHeight,
				} as React.CSSProperties
			}>
			{/* Background Features */}
			<HeroBackground />

			{/* Particle Background */}
			<div className='absolute inset-0'>
				{/* Animated background particles */}
				{Array.from({ length: 20 }).map((_, i) => (
					<div
						key={i}
						className='absolute w-2 h-2 rounded-full opacity-30 animate-pulse'
						style={{
							left: `${Math.random() * 100}%`,
							top: `${Math.random() * 100}%`,
							background:
								i % 3 === 0
									? colors.brand.blue
									: i % 3 === 1
									? colors.brand.green
									: colors.supporting.lightBlue,
							animationDelay: `${Math.random() * 3}s`,
							animationDuration: `${2 + Math.random() * 2}s`,
						}}></div>
				))}
			</div>

			{/* Main Content Container */}
			<div
				className='relative h-full'
				style={{
					padding,
				}}>
				<div
					className='max-w-7xl mx-auto h-full'
					style={{
						padding: `0 ${padding}`,
					}}>
					{/* Mobile Layout - Single Column */}
					{screenSize === 'mobile' && (
						<div
							className='flex flex-col h-full justify-center'
							style={{
								gap: padding,
							}}>
							{/* Company Branding - Centered */}
							<div className='flex justify-center flex-shrink-0'>
								<div className='w-full max-w-sm'>
									<HeroBranding />
								</div>
							</div>

							{/* Hero Preview - Stacked */}
							<div className='flex justify-center flex-shrink-0'>
								<div className='w-full max-w-sm'>
									<HeroPreview onGetStarted={onGetStarted} />
								</div>
							</div>
						</div>
					)}

					{/* Tablet Layout - Single Column with more space */}
					{screenSize === 'tablet' && (
						<div
							className='flex flex-col h-full justify-center'
							style={{
								gap: padding,
							}}>
							{/* Company Branding - Centered */}
							<div className='flex justify-center flex-shrink-0'>
								<div className='w-full max-w-lg'>
									<HeroBranding />
								</div>
							</div>

							{/* Hero Preview - Stacked */}
							<div className='flex justify-center flex-shrink-0'>
								<div className='w-full max-w-xl'>
									<HeroPreview onGetStarted={onGetStarted} />
								</div>
							</div>
						</div>
					)}

					{/* Desktop Layout - Two Column */}
					{screenSize === 'desktop' && (
						<div
							className='grid grid-cols-2 items-center h-full'
							style={{
								gap: padding,
							}}>
							{/* Left Side - Company Branding */}
							<div className='flex items-center justify-start'>
								<HeroBranding />
							</div>

							{/* Right Side - Hero Preview */}
							<div className='flex items-center justify-center'>
								<HeroPreview onGetStarted={onGetStarted} />
							</div>
						</div>
					)}
				</div>
			</div>

			{/* Scroll Indicator - Only show on larger screens */}
			<div className='absolute bottom-4 sm:bottom-6 md:bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce hidden sm:block'>
				<div
					className='w-5 h-8 sm:w-6 sm:h-10 border-2 rounded-full flex justify-center'
					style={{ borderColor: colors.brand.blue }}>
					<div
						className='w-1 h-2 sm:h-3 rounded-full mt-1 sm:mt-2 animate-pulse'
						style={{ background: colors.brand.blue }}></div>
				</div>
			</div>
		</div>
	);
};

export default HeroSection;
