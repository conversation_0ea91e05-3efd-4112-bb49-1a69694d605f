/** @format */

'use client';

import { colors } from '@/app/colors';
import { useViewportHeight } from '@/app/landing/utils/responsiveUtils';
import { LANDING_PAGE_DATA } from '@/app/shared/poi/constants';
import React from 'react';
import { FiMapPin } from 'react-icons/fi';
import DynamicCategoryMosaic from './DynamicCategoryMosaic';

interface CategoryExplorerProps {
	onCategorySelect?: (category: any) => void;
}

const CategoryExplorer: React.FC<CategoryExplorerProps> = ({
	onCategorySelect,
}) => {
	// Use viewport height for responsive section sizing
	const { screenSize, minHeight, padding } = useViewportHeight('section');

	return (
		<div
			className='bg-transparent viewport-section'
			style={{
				minHeight,
				padding: `${padding} 0`,
			}}>
			<div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
				{/* Section Header */}
				<div className='text-center mb-12'>
					<div
						className='inline-flex items-center space-x-2 rounded-full px-6 py-3 mb-8 border'
						style={{
							background: `linear-gradient(135deg, ${colors.ui.green50} 0%, ${colors.ui.blue50} 100%)`,
							borderColor: colors.ui.gray200,
						}}>
						<FiMapPin
							className='w-5 h-5'
							style={{ color: colors.brand.blue }}
						/>
						<span
							className='text-sm font-medium'
							style={{ color: colors.neutral.textBlack }}>
							{LANDING_PAGE_DATA.sectionHeaders.categoryExplorer.badge}
						</span>
					</div>

					<h2 className='text-5xl md:text-6xl font-bold mb-6'>
						<span style={{ color: colors.brand.navy }}>
							{LANDING_PAGE_DATA.sectionHeaders.categoryExplorer.title}
						</span>
						<br />
						<span
							className='text-transparent bg-clip-text'
							style={{
								backgroundImage: `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.green} 100%)`,
							}}>
							{LANDING_PAGE_DATA.sectionHeaders.categoryExplorer.subtitle}
						</span>
					</h2>

					<p
						className='text-xl max-w-3xl mx-auto leading-relaxed mb-8'
						style={{ color: colors.neutral.slateGray }}>
						{LANDING_PAGE_DATA.sectionHeaders.categoryExplorer.description}
					</p>
				</div>

				{/* Category Mosaic - Using the old design */}
				<DynamicCategoryMosaic onCategorySelect={onCategorySelect} />
			</div>
		</div>
	);
};

export default CategoryExplorer;
