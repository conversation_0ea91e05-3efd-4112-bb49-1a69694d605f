/** @format */

'use client';

import { colors } from '@/app/colors';
import Image from 'next/image';
import React from 'react';
import { FiHeart } from 'react-icons/fi';

const LandingFooter: React.FC = () => {
	return (
		<footer className='py-8 sm:py-10 lg:py-12 relative bg-transparent'>
			<div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
				<div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8'>
					{/* Brand Section */}
					<div className='col-span-1 sm:col-span-2 lg:col-span-2'>
						<div className='flex items-center space-x-3 mb-4'>
							<div className='w-8 h-8 sm:w-10 sm:h-10 relative'>
								<Image
									src='/logo/512x512.png'
									alt='Wizlop Logo'
									width={40}
									height={40}
									className='rounded-xl'
								/>
							</div>
							<h3
								className='text-xl sm:text-2xl font-bold'
								style={{ color: colors.neutral.textBlack }}>
								Wizlop
							</h3>
						</div>
						<p
							className='mb-4 max-w-md leading-relaxed text-sm sm:text-base'
							style={{ color: colors.neutral.slateGray }}>
							Revolutionizing location discovery through conversational AI. Find
							exactly what you're looking for in Istanbul.
						</p>
						<div
							className='flex items-center text-sm sm:text-base'
							style={{ color: colors.neutral.slateGray }}>
							<span>Made with</span>
							<FiHeart
								className='mx-2 w-4 h-4'
								style={{ color: colors.brand.green }}
							/>
							<span>in Istanbul</span>
						</div>
					</div>

					{/* Quick Links */}
					<div className='mt-6 sm:mt-0'>
						<h4
							className='text-base sm:text-lg font-bold mb-3 sm:mb-4'
							style={{ color: colors.neutral.textBlack }}>
							Quick Links
						</h4>
						<ul className='space-y-2'>
							<li>
								<button
									className='transition-colors text-left text-xs sm:text-sm hover:underline'
									style={{ color: colors.neutral.slateGray }}
									onMouseEnter={(e) => {
										e.currentTarget.style.color = colors.brand.blue;
									}}
									onMouseLeave={(e) => {
										e.currentTarget.style.color = colors.neutral.slateGray;
									}}>
									Explore Categories
								</button>
							</li>
							<li>
								<button
									className='transition-colors text-left text-xs sm:text-sm hover:underline'
									style={{ color: colors.neutral.slateGray }}
									onMouseEnter={(e) => {
										e.currentTarget.style.color = colors.brand.blue;
									}}
									onMouseLeave={(e) => {
										e.currentTarget.style.color = colors.neutral.slateGray;
									}}>
									AI Search
								</button>
							</li>
							<li>
								<button
									className='transition-colors text-left text-xs sm:text-sm hover:underline'
									style={{ color: colors.neutral.slateGray }}
									onMouseEnter={(e) => {
										e.currentTarget.style.color = colors.brand.blue;
									}}
									onMouseLeave={(e) => {
										e.currentTarget.style.color = colors.neutral.slateGray;
									}}>
									Globe View
								</button>
							</li>
						</ul>
					</div>

					{/* Company Links */}
					<div className='mt-6 sm:mt-0'>
						<h4
							className='text-base sm:text-lg font-bold mb-3 sm:mb-4'
							style={{ color: colors.neutral.textBlack }}>
							Company
						</h4>
						<ul className='space-y-2'>
							<li>
								<button
									className='transition-colors text-left text-xs sm:text-sm hover:underline'
									style={{ color: colors.neutral.slateGray }}
									onMouseEnter={(e) => {
										e.currentTarget.style.color = colors.brand.blue;
									}}
									onMouseLeave={(e) => {
										e.currentTarget.style.color = colors.neutral.slateGray;
									}}>
									About Us
								</button>
							</li>
							<li>
								<button
									className='transition-colors text-left text-xs sm:text-sm hover:underline'
									style={{ color: colors.neutral.slateGray }}
									onMouseEnter={(e) => {
										e.currentTarget.style.color = colors.brand.blue;
									}}
									onMouseLeave={(e) => {
										e.currentTarget.style.color = colors.neutral.slateGray;
									}}>
									Contact
								</button>
							</li>
							<li>
								<button
									className='transition-colors text-left text-xs sm:text-sm hover:underline'
									style={{ color: colors.neutral.slateGray }}
									onMouseEnter={(e) => {
										e.currentTarget.style.color = colors.brand.blue;
									}}
									onMouseLeave={(e) => {
										e.currentTarget.style.color = colors.neutral.slateGray;
									}}>
									Privacy Policy
								</button>
							</li>
						</ul>
					</div>
				</div>

				{/* Bottom Section */}
				<div
					className='border-t mt-6 sm:mt-8 pt-4 sm:pt-6 text-center'
					style={{ borderColor: colors.ui.gray200 }}>
					<p
						className='text-xs sm:text-sm'
						style={{ color: colors.neutral.slateGray }}>
						© 2024 Wizlop. All rights reserved.
						<span className='hidden sm:inline'>
							{' '}
							• Intelligent Location Discovery Platform
						</span>
					</p>
				</div>
			</div>
		</footer>
	);
};

export default LandingFooter;
