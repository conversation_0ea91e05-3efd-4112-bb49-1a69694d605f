/**
 * POI-related constants and field definitions
 *
 * @format
 */

// Fields that can be edited when submitting or updating POIs
export const POI_EDITABLE_FIELDS = [
	'name',
	'name_en',
	'name_tr',
	'name_uk',
	'name_de',
	'name_ru',
	'name_ar',
	'category',
	'subcategory',
	'cuisine',
	'full_address',
	'street',
	'neighborhood',
	'district',
	'city',
	'province',
	'country',
	'phone_number',
	'opening_hours',
	'description',
	'latitude',
	'longitude',
];

// POI categories and subcategories structure
export const POI_CATEGORIES_DATA = {
	'Food & Drink': {
		subcategories: [
			'Cafe',
			'Restaurant',
			'Bar',
			'Street Food',
			'Fine Dining',
			'Bakery',
			'Fast Food',
			'Food Truck',
			'Brewery',
			'Wine Bar',
			'Cocktail Bar',
			'Rooftop Bar',
		],
	},
	'Cultural & Creative Experiences': {
		subcategories: [
			'Museum Visit',
			'Art Gallery Walk',
			'Theater',
			'Concert',
			'Festival',
			'Workshop',
			'Art Studio',
			'Cultural Center',
			'Opera House',
			'Dance Performance',
			'Poetry Reading',
			'Book Reading',
		],
	},
	'Sports & Fitness': {
		subcategories: [
			'Gym',
			'Yoga Studio',
			'Swimming',
			'Rock Climbing',
			'Tennis',
			'Basketball',
			'Football',
			'Running Track',
			'Martial Arts',
			'Pilates',
			'CrossFit',
			'Boxing',
		],
	},
	Entertainment: {
		subcategories: [
			'Cinema',
			'Bowling',
			'Karaoke',
			'Gaming',
			'Comedy Club',
			'Dance Club',
			'Arcade',
			'Mini Golf',
			'Escape Room',
			'Board Game Cafe',
			'Pool Hall',
			'Laser Tag',
		],
	},
	'Shopping & Markets': {
		subcategories: [
			'Mall',
			'Boutique',
			'Market',
			'Vintage Store',
			'Bookstore',
			'Electronics',
			'Farmers Market',
			'Antique Shop',
			'Jewelry Store',
			'Clothing Store',
			'Shoe Store',
			'Gift Shop',
		],
	},
	'Outdoor & Nature': {
		subcategories: [
			'Park',
			'Beach',
			'Hiking Trail',
			'Garden',
			'Lake',
			'Forest',
			'Mountain',
			'Waterfall',
			'Botanical Garden',
			'Nature Reserve',
			'Camping',
			'Picnic Area',
		],
	},
	'Wellness & Beauty': {
		subcategories: [
			'Spa',
			'Massage',
			'Hair Salon',
			'Nail Salon',
			'Beauty Clinic',
			'Wellness Center',
			'Meditation Center',
			'Sauna',
			'Hot Springs',
			'Acupuncture',
			'Chiropractor',
			'Dermatologist',
		],
	},
	Transportation: {
		subcategories: [
			'Metro Station',
			'Bus Stop',
			'Airport',
			'Train Station',
			'Taxi Stand',
			'Car Rental',
			'Bike Rental',
			'Ferry Terminal',
			'Parking',
			'Gas Station',
			'Electric Charging',
			'Scooter Rental',
		],
	},
};

// Helper functions to get categories and subcategories
export const getPOICategories = (): string[] => {
	return Object.keys(POI_CATEGORIES_DATA);
};

export const getPOISubcategories = (category?: string): string[] => {
	if (!category) {
		// Return all subcategories if no category specified
		return Object.values(POI_CATEGORIES_DATA).flatMap(
			(cat) => cat.subcategories
		);
	}

	const categoryData =
		POI_CATEGORIES_DATA[category as keyof typeof POI_CATEGORIES_DATA];
	return categoryData ? categoryData.subcategories : [];
};

// Legacy exports for backward compatibility
export const POI_CATEGORIES = getPOICategories();
export const POI_SUBCATEGORY_OPTIONS = getPOISubcategories();

// Submission reasons for POI reports/updates
export const POI_SUBMISSION_REASONS = [
	{ value: 'new_poi', label: 'New POI' },
	{ value: 'missing_info', label: 'Missing Information' },
	{ value: 'incorrect_info', label: 'Incorrect Information' },
	{ value: 'closed_business', label: 'Business Closed' },
	{ value: 'moved_location', label: 'Location Changed' },
];

// Landing Page Constants
export const LANDING_PAGE_DATA = {
	// Company branding
	branding: {
		companyName: 'Wizlop',
		tagline: 'Smart Location Discovery Platform',
		description:
			'Discover amazing places worldwide through intelligent search and exploration tools',
		heroTitle: 'Discover Amazing Places Worldwide',
		heroDescription:
			'Experience the future of location discovery with AI-powered search, category exploration, and intelligent recommendations. Currently available in Turkey with global expansion coming soon.',
	},

	// Feature badges - will be dynamically populated with actual counts
	getFeatureBadges: () => {
		const totalCategories = Object.keys(POI_CATEGORIES_DATA).length;
		const totalSubcategories = Object.values(POI_CATEGORIES_DATA).reduce(
			(total, category) => total + category.subcategories.length,
			0
		);
		return [
			{
				icon: 'FiGlobe',
				text: `${totalCategories} Categories • ${totalSubcategories} Subcategories`,
			},
			{ icon: 'FiMessageCircle', text: 'AI-Powered Search' },
			{ icon: 'FiMapPin', text: 'Smart Exploration' },
		];
	},

	// Live search preview data
	searchPreview: {
		title: 'Live Search Preview',
		subtitle: 'Real locations in Turkey',
		locations: [
			{ name: 'Karaköy Lokantası', type: 'Restaurant', rating: 4.8 },
			{ name: 'Galata Tower Cafe', type: 'Cafe', rating: 4.6 },
			{ name: 'Cihangir Yoga Studio', type: 'Wellness', rating: 4.9 },
		],
	},

	// AI conversation demo
	aiConversation: {
		title: 'AI Conversation',
		subtitle: 'Natural language search',
		userMessage: 'Find me a cozy cafe with good wifi in Istanbul',
		aiResponse:
			'I found 8 cozy cafes with excellent wifi in Istanbul. Here are the top 3 based on ambiance and connectivity...',
		typingIndicator: 'AI is typing...',
	},

	// Multiple conversation examples for looping animation
	conversationExamples: [
		{
			userMessage: 'Find me a cozy cafe with good wifi in Istanbul',
			aiResponse:
				'I found 8 cozy cafes with excellent wifi in Istanbul. Here are the top 3 based on ambiance and connectivity...',
		},
		{
			userMessage: 'Show me romantic restaurants for a date night',
			aiResponse:
				'Perfect! I found 12 romantic restaurants with intimate ambiance. Here are the most highly rated options...',
		},
		{
			userMessage: 'Where can I find outdoor activities near the Bosphorus?',
			aiResponse:
				'Great choice! I discovered 15 outdoor activities along the Bosphorus. Here are the most popular ones...',
		},
	],

	// Section headers
	sectionHeaders: {
		categoryExplorer: {
			badge: 'Explore Categories',
			title: 'Explore',
			subtitle: 'Every Category',
			description:
				'From adrenaline-pumping adventures to peaceful retreats. Discover amazing locations across Turkey and beyond.',
		},
		aiDemo: {
			badge: 'AI & Smart Search',
			title: 'See AI',
			subtitle: 'In Action',
			description:
				'Watch how our AI understands context, processes natural language, and delivers personalized results alongside powerful category-based exploration.',
		},
	},

	// AI Demo scenarios
	aiDemoScenarios: [
		{
			id: 'cozy-cafe',
			userQuery:
				'I want a cozy cafe with good wifi near Galata Tower where I can work for a few hours',
			aiResponse:
				'I found 8 cozy cafes with excellent wifi near Galata Tower. Based on your work needs, here are the top 3 with quiet atmospheres and reliable internet...',
			searchResults: [
				{
					name: 'Karakoy Lokantasi Cafe',
					type: 'Cafe',
					rating: 4.8,
					distance: '200m from Galata Tower',
					features: ['Free WiFi', 'Quiet', 'Power Outlets', 'Coffee'],
				},
				{
					name: 'Galata Coffee Roasters',
					type: 'Coffee Shop',
					rating: 4.6,
					distance: '150m from Galata Tower',
					features: ['High-Speed WiFi', 'Work-Friendly', 'Specialty Coffee'],
				},
				{
					name: 'Minimalist Cafe',
					type: 'Cafe',
					rating: 4.7,
					distance: '300m from Galata Tower',
					features: ['Silent Zone', 'Fast WiFi', 'Laptop-Friendly'],
				},
			],
			processingSteps: [
				'Understanding: cozy cafe + good wifi + work environment',
				'Location: Galata Tower area (radius: 500m)',
				'Filtering: WiFi quality ratings > 4.0',
				'Ranking: Coziness score + Work-friendliness',
				'Context: Work duration (few hours) = quiet preference',
			],
			traditionalSteps: [
				"Select 'Cafe' from category dropdown",
				"Choose 'Galata Tower' area on map",
				"Filter by 'WiFi Available'",
				'Sort by rating or distance',
				'Manually check each result for work suitability',
			],
		},
		{
			id: 'romantic-dinner',
			userQuery:
				'Looking for a romantic restaurant with Bosphorus view for anniversary dinner',
			aiResponse:
				'Perfect! I found several romantic restaurants with stunning Bosphorus views. Here are 3 ideal spots for your anniversary celebration...',
			searchResults: [
				{
					name: 'Sunset Grill & Bar',
					type: 'Fine Dining',
					rating: 4.9,
					distance: 'Ulus, Bosphorus view',
					features: [
						'Bosphorus View',
						'Romantic',
						'Fine Dining',
						'Reservations',
					],
				},
				{
					name: 'Lacivert Restaurant',
					type: 'Seafood',
					rating: 4.7,
					distance: 'Anadolu Hisarı',
					features: ['Waterfront', 'Intimate', 'Seafood', 'Sunset View'],
				},
				{
					name: 'Feriye Palace Restaurant',
					type: 'Ottoman Cuisine',
					rating: 4.8,
					distance: 'Ortaköy',
					features: [
						'Historic',
						'Bosphorus View',
						'Elegant',
						'Special Occasions',
					],
				},
			],
			processingSteps: [
				'Understanding: romantic + restaurant + Bosphorus view + anniversary',
				'Location: Bosphorus waterfront areas',
				'Filtering: Romantic atmosphere + water view',
				'Ranking: Romance score + view quality + special occasion suitability',
				'Context: Anniversary = special occasion preferences',
			],
			traditionalSteps: [
				"Select 'Restaurant' category",
				"Filter by 'Waterfront' or 'View'",
				'Search multiple areas along Bosphorus',
				'Check individual photos for view quality',
				'Read reviews for romantic atmosphere confirmation',
			],
		},
	],
};
