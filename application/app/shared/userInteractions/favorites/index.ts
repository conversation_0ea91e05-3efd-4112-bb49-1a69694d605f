/** @format */

// Export all unified favorites/saves functionality

export * from '@/app/shared/userInteractions/favorites/services';
export * from '@/app/shared/userInteractions/favorites/types';
// export * from '@/app/shared/userInteractions/favorites/hooks' // Commented out - hooks directory is empty
export * from '@/app/shared/userInteractions/favorites/components';

// Legacy exports for backward compatibility
export { FavoritesService as SavesService } from '@/app/shared/userInteractions/favorites/services';
